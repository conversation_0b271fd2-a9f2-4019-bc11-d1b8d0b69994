{

{
  "process_name": "CO2EmesseRetragasProcess",
  "purpose": "Integrazione dati emissioni CO2 contatori gas Retragas verso sistema centrale NDUE per compliance ambientale",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI - stored procedure ACSCO2EmesseRetragasPollingStmt",
      "format": "XML",
      "encoding": "UTF-8",
      "schema": "ACSCO2EmesseRetragasPolling_Type"
    },
    "output": {
      "type": "SOAP 1.1/1.2",
      "target": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx",
      "format": "XML",
      "schema": "N2ConsumiRetragas.ImportazioneRetragas"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "pattern": "File→Process→Detail hierarchy",
    "status_management": "transactionStatus (ToProcess→InProcess→Failed→Completed)"
  },
  "transformations": [
    {
      "map": "ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm", 
      "purpose": "Update processing status in staging tables"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseRetragasProcess.odx", "CO2EmesseRetragasStaging.odx"],
    "receive_ports": ["ACSCO2EmesseRetragasPolling"],
    "send_ports": ["WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap", "WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap12", "ACSCO2EmesseRetragasUpdateStatus"],
    "schemas": ["ACSCO2EmesseRetragasPolling.xsd", "N2ConsumiRetragas.xsd", "ACSCO2EmesseRetragasInsertTypedProcedure.xsd"],
    "maps": ["ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm", "ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm"],
    "bindings": ["N2ConsumiRetragas.BindingInfo.xml", "ACSCO2EmesseRetragasInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseRetragasProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseRetragasStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseRetragasPolling.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/N2ConsumiRetragas.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiRetragas.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseRetragasPollingStmt.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseRetragasInsert.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for business tracking",
    "trace_id": "End-to-end correlation via MsysEaiFx",
    "message_tracking": "Complete audit trail in staging tables",
    "notifications": "Proactive alerting via NotificationService",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation SERIALIZABLE",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "CO2EmesseRetragasStaging",
  "purpose": "Staging di file dati CO2 contatori gas Retragas in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-8",
      "schema": "CO2EmesseRetragas",
      "pattern": "unknown"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSCO2EmesseRetragasInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSCO2EmesseRetragasInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy"
  },
  "transformations": [
    {
      "map": "CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseRetragasStaging.odx"],
    "receive_ports": ["CO2EmesseRetragasIn"],
    "send_ports": ["ACSCO2EmesseRetragasInsertOut"],
    "schemas": ["CO2EmesseRetragas.xsd", "ACSCO2EmesseRetragasInsertTypedProcedure.xsd"],
    "maps": ["CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm"],
    "bindings": ["ACSCO2EmesseRetragasInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseRetragasStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseRetragas.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseRetragas.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseRetragasInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseRetragasInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSCO2EmesseRetragasInsert.bindinginfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseRetragasInsert.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with daily/monthly CO2 data, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation SERIALIZABLE",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas", "Proprietary mapping"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input"]
  }
},
{
  "process_name": "CO2EmesseUnaretiProcess",
  "purpose": "Integrazione dati emissioni CO2 contatori gas Unareti verso sistema centrale NDUE per compliance ambientale",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI - stored procedure ACSCO2EmesseUnaretiPollingStmt",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "ACSCO2EmesseUnaretiPolling_Type"
    },
    "output": {
      "type": "SOAP 1.1/1.2",
      "target": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiUnareti.asmx",
      "format": "XML",
      "schema": "N2ConsumiUnareti.ImportazioneUnareti"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "pattern": "File→Process→Detail hierarchy",
    "status_management": "transactionStatus (ToProcess→InProcess→Failed→Completed)",
    "acs_kpi_type": "CO2EmesseUnareti"
  },
  "transformations": [
    {
      "map": "ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm",
      "purpose": "Update processing status in staging tables"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseUnaretiProcess.odx"],
    "receive_ports": ["ACSCO2EmesseUnaretiPollingIn"],
    "send_ports": ["WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap", "WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap12", "ACSUpdateStatusOut"],
    "schemas": ["ACSCO2EmesseUnaretiPolling.xsd", "N2ConsumiUnareti.xsd", "ACSUpdateStatusTypedProcedure.xsd"],
    "maps": ["ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm", "ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm"],
    "bindings": ["N2ConsumiUnareti.BindingInfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseUnaretiProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiPolling.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiPolling.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/N2ConsumiUnareti.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/N2ConsumiUnareti.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSUpdateStatusTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiUnareti.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseUnaretiPollingStmt.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for business tracking",
    "trace_id": "End-to-end correlation via MsysEaiFx",
    "message_tracking": "Complete audit trail in staging tables",
    "notifications": "Proactive alerting via NotificationService",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation READ COMMITTED",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "CO2EmesseUnaretiStaging",
  "purpose": "Staging di file dati CO2 contatori gas Unareti in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-8",
      "schema": "CO2EmesseUnareti",
      "pattern": "unknown"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSCO2EmesseUnaretiInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSCO2EmesseUnaretiInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy",
    "acs_kpi_type": "CO2EmesseUnareti"
  },
  "transformations": [
    {
      "map": "CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseUnaretiStaging.odx"],
    "receive_ports": ["CO2EmesseUnaretiIn"],
    "send_ports": ["ACSCO2EmesseUnaretiInsertOut"],
    "schemas": ["CO2EmesseUnareti.xsd", "ACSCO2EmesseUnaretiInsertTypedProcedure.xsd"],
    "maps": ["CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm"],
    "bindings": ["unknown"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseUnaretiStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseUnareti.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseUnareti.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseUnaretiInsert.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with daily/monthly CO2 data, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation SERIALIZABLE",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas", "Proprietary mapping"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input"]
  }
},
{
  "process_name": "VenditaCaloreBresciaProcess",
  "purpose": "Integrazione dati vendita calore impianti Brescia verso sistema NDUE tramite polling staging e SOAP",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "ACSVenditaCaloreBresciaPolling_Type",
      "stored_procedure": "ACSVenditaCaloreBresciaPollingStmt"
    },
    "output": {
      "type": "SOAP Web Service",
      "target": "NDUE ImportazioneRicavi",
      "format": "XML",
      "schema": "N2VenditaCalore",
      "endpoint": "http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx"
    }
  },
  "staging": {
    "tables": ["ACSKPIProcess", "ACSKPIDetail", "ACSKPIConfiguration"],
    "stored_procedure": "ACSVenditaCaloreBresciaPollingStmt",
    "pattern": "Polling TOP 10 with retry logic and transaction status management",
    "acs_kpi_type": "VenditaCaloreBrescia"
  },
  "transformations": [
    {
      "map": "ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm",
      "purpose": "Status update for processed records"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["VenditaCaloreBresciaProcess.odx"],
    "receive_ports": ["ACSVenditaCaloreBresciaPollingIn"],
    "send_ports": ["N2VenditaCaloreOut", "ACSUpdateStatusOut"],
    "schemas": ["ACSVenditaCaloreBresciaPolling.Type.xsd", "N2VenditaCalore.Type.xsd"],
    "maps": ["ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm", "ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm"],
    "bindings": ["N2VenditaCalore.BindingInfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/VenditaCaloreBresciaProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreBresciaPolling.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/N2VenditaCalore.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2VenditaCalore.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSVenditaCaloreBresciaPollingStmt.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "Transaction status in ACSKPIProcess table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation READ COMMITTED",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "VenditaCaloreBresciaStaging",
  "purpose": "Staging di file dati vendita calore impianti Brescia in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "VenditaCaloreBrescia",
      "pattern": "VenditaCalore-Brescia-YYYY"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSVenditaCaloreBresciaInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSVenditaCaloreBresciaInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy with UNPIVOT transformation",
    "acs_kpi_type": "VenditaCaloreBrescia"
  },
  "transformations": [
    {
      "map": "VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters with ACSGenericRowType"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["VenditaCaloreBresciaStaging.odx"],
    "receive_ports": ["VenditaCaloreBresciaIn"],
    "send_ports": ["ACSVenditaCaloreBresciaInsertOut"],
    "schemas": ["VenditaCaloreBrescia.xsd", "VenditaCaloreBrescia.Type.xsd", "ACSVenditaCaloreBresciaInsertTypedProcedure.xsd"],
    "maps": ["VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm"],
    "bindings": ["ACSVenditaCaloreBresciaInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/VenditaCaloreBresciaStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/VenditaCaloreBrescia.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/VenditaCaloreBrescia.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreBresciaInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreBresciaInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSVenditaCaloreBresciaInsert.bindinginfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSVenditaCaloreBresciaInsert.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/User Defined Types/ACSGenericRowType.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with annual heat sales data for Brescia plants, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation READ COMMITTED",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas with generic v0-v31 fields", "Complex UNPIVOT stored procedure logic"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and complex SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input", "Rigid filename validation"]
  }
},


}