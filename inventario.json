{

{
  "process_name": "CO2EmesseRetragasProcess",
  "purpose": "Integrazione dati emissioni CO2 contatori gas Retragas verso sistema centrale NDUE per compliance ambientale",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI - stored procedure ACSCO2EmesseRetragasPollingStmt",
      "format": "XML",
      "encoding": "UTF-8",
      "schema": "ACSCO2EmesseRetragasPolling_Type"
    },
    "output": {
      "type": "SOAP 1.1/1.2",
      "target": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx",
      "format": "XML",
      "schema": "N2ConsumiRetragas.ImportazioneRetragas"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "pattern": "File→Process→Detail hierarchy",
    "status_management": "transactionStatus (ToProcess→InProcess→Failed→Completed)"
  },
  "transformations": [
    {
      "map": "ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm", 
      "purpose": "Update processing status in staging tables"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseRetragasProcess.odx", "CO2EmesseRetragasStaging.odx"],
    "receive_ports": ["ACSCO2EmesseRetragasPolling"],
    "send_ports": ["WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap", "WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap12", "ACSCO2EmesseRetragasUpdateStatus"],
    "schemas": ["ACSCO2EmesseRetragasPolling.xsd", "N2ConsumiRetragas.xsd", "ACSCO2EmesseRetragasInsertTypedProcedure.xsd"],
    "maps": ["ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm", "ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm"],
    "bindings": ["N2ConsumiRetragas.BindingInfo.xml", "ACSCO2EmesseRetragasInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseRetragasProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseRetragasStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseRetragasPolling.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/N2ConsumiRetragas.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiRetragas.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseRetragasPollingStmt.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseRetragasInsert.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for business tracking",
    "trace_id": "End-to-end correlation via MsysEaiFx",
    "message_tracking": "Complete audit trail in staging tables",
    "notifications": "Proactive alerting via NotificationService",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation SERIALIZABLE",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "CO2EmesseRetragasStaging",
  "purpose": "Staging di file dati CO2 contatori gas Retragas in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-8",
      "schema": "CO2EmesseRetragas",
      "pattern": "unknown"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSCO2EmesseRetragasInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSCO2EmesseRetragasInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy"
  },
  "transformations": [
    {
      "map": "CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseRetragasStaging.odx"],
    "receive_ports": ["CO2EmesseRetragasIn"],
    "send_ports": ["ACSCO2EmesseRetragasInsertOut"],
    "schemas": ["CO2EmesseRetragas.xsd", "ACSCO2EmesseRetragasInsertTypedProcedure.xsd"],
    "maps": ["CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm"],
    "bindings": ["ACSCO2EmesseRetragasInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseRetragasStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseRetragas.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseRetragas.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseRetragasInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseRetragasInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSCO2EmesseRetragasInsert.bindinginfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseRetragasInsert.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with daily/monthly CO2 data, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation SERIALIZABLE",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas", "Proprietary mapping"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input"]
  }
},
{
  "process_name": "CO2EmesseUnaretiProcess",
  "purpose": "Integrazione dati emissioni CO2 contatori gas Unareti verso sistema centrale NDUE per compliance ambientale",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI - stored procedure ACSCO2EmesseUnaretiPollingStmt",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "ACSCO2EmesseUnaretiPolling_Type"
    },
    "output": {
      "type": "SOAP 1.1/1.2",
      "target": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiUnareti.asmx",
      "format": "XML",
      "schema": "N2ConsumiUnareti.ImportazioneUnareti"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "pattern": "File→Process→Detail hierarchy",
    "status_management": "transactionStatus (ToProcess→InProcess→Failed→Completed)",
    "acs_kpi_type": "CO2EmesseUnareti"
  },
  "transformations": [
    {
      "map": "ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm",
      "purpose": "Update processing status in staging tables"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseUnaretiProcess.odx"],
    "receive_ports": ["ACSCO2EmesseUnaretiPollingIn"],
    "send_ports": ["WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap", "WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap12", "ACSUpdateStatusOut"],
    "schemas": ["ACSCO2EmesseUnaretiPolling.xsd", "N2ConsumiUnareti.xsd", "ACSUpdateStatusTypedProcedure.xsd"],
    "maps": ["ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm", "ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm"],
    "bindings": ["N2ConsumiUnareti.BindingInfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseUnaretiProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiPolling.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiPolling.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/N2ConsumiUnareti.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/N2ConsumiUnareti.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSUpdateStatusTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiUnareti.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseUnaretiPollingStmt.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for business tracking",
    "trace_id": "End-to-end correlation via MsysEaiFx",
    "message_tracking": "Complete audit trail in staging tables",
    "notifications": "Proactive alerting via NotificationService",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation READ COMMITTED",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "CO2EmesseUnaretiStaging",
  "purpose": "Staging di file dati CO2 contatori gas Unareti in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-8",
      "schema": "CO2EmesseUnareti",
      "pattern": "unknown"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSCO2EmesseUnaretiInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSCO2EmesseUnaretiInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy",
    "acs_kpi_type": "CO2EmesseUnareti"
  },
  "transformations": [
    {
      "map": "CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["CO2EmesseUnaretiStaging.odx"],
    "receive_ports": ["CO2EmesseUnaretiIn"],
    "send_ports": ["ACSCO2EmesseUnaretiInsertOut"],
    "schemas": ["CO2EmesseUnareti.xsd", "ACSCO2EmesseUnaretiInsertTypedProcedure.xsd"],
    "maps": ["CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm"],
    "bindings": ["unknown"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/CO2EmesseUnaretiStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/CO2Emesse/CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseUnareti.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/CO2EmesseUnareti.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/CO2Emesse/ACSCO2EmesseUnaretiInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSCO2EmesseUnaretiInsert.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with daily/monthly CO2 data, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation SERIALIZABLE",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas", "Proprietary mapping"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input"]
  }
},
{
  "process_name": "VenditaCaloreBresciaProcess",
  "purpose": "Integrazione dati vendita calore impianti Brescia verso sistema NDUE tramite polling staging e SOAP",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "ACSVenditaCaloreBresciaPolling_Type",
      "stored_procedure": "ACSVenditaCaloreBresciaPollingStmt"
    },
    "output": {
      "type": "SOAP Web Service",
      "target": "NDUE ImportazioneRicavi",
      "format": "XML",
      "schema": "N2VenditaCalore",
      "endpoint": "http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx"
    }
  },
  "staging": {
    "tables": ["ACSKPIProcess", "ACSKPIDetail", "ACSKPIConfiguration"],
    "stored_procedure": "ACSVenditaCaloreBresciaPollingStmt",
    "pattern": "Polling TOP 10 with retry logic and transaction status management",
    "acs_kpi_type": "VenditaCaloreBrescia"
  },
  "transformations": [
    {
      "map": "ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm",
      "purpose": "Status update for processed records"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["VenditaCaloreBresciaProcess.odx"],
    "receive_ports": ["ACSVenditaCaloreBresciaPollingIn"],
    "send_ports": ["N2VenditaCaloreOut", "ACSUpdateStatusOut"],
    "schemas": ["ACSVenditaCaloreBresciaPolling.Type.xsd", "N2VenditaCalore.Type.xsd"],
    "maps": ["ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm", "ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm"],
    "bindings": ["N2VenditaCalore.BindingInfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/VenditaCaloreBresciaProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreBresciaPolling.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/N2VenditaCalore.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2VenditaCalore.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSVenditaCaloreBresciaPollingStmt.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "Transaction status in ACSKPIProcess table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation READ COMMITTED",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "VenditaCaloreBresciaStaging",
  "purpose": "Staging di file dati vendita calore impianti Brescia in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "VenditaCaloreBrescia",
      "pattern": "VenditaCalore-Brescia-YYYY"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSVenditaCaloreBresciaInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSVenditaCaloreBresciaInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy with UNPIVOT transformation",
    "acs_kpi_type": "VenditaCaloreBrescia"
  },
  "transformations": [
    {
      "map": "VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters with ACSGenericRowType"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["VenditaCaloreBresciaStaging.odx"],
    "receive_ports": ["VenditaCaloreBresciaIn"],
    "send_ports": ["ACSVenditaCaloreBresciaInsertOut"],
    "schemas": ["VenditaCaloreBrescia.xsd", "VenditaCaloreBrescia.Type.xsd", "ACSVenditaCaloreBresciaInsertTypedProcedure.xsd"],
    "maps": ["VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm"],
    "bindings": ["ACSVenditaCaloreBresciaInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/VenditaCaloreBresciaStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/VenditaCaloreBrescia.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/VenditaCaloreBrescia.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreBresciaInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreBresciaInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSVenditaCaloreBresciaInsert.bindinginfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSVenditaCaloreBresciaInsert.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/User Defined Types/ACSGenericRowType.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with annual heat sales data for Brescia plants, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation READ COMMITTED",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas with generic v0-v31 fields", "Complex UNPIVOT stored procedure logic"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and complex SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input", "Rigid filename validation"]
  }
},
{
  "process_name": "VenditaCaloreMilanoProcess",
  "purpose": "Integrazione dati vendita calore impianti Milano verso sistema NDUE tramite polling staging e SOAP",
  "io": {
    "input": {
      "type": "SQL TypedPolling",
      "source": "Database EAI",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "ACSVenditaCaloreMilanoPolling_Type",
      "stored_procedure": "ACSVenditaCaloreMilanoPollingStmt"
    },
    "output": {
      "type": "SOAP Web Service",
      "target": "NDUE ImportazioneRicavi",
      "format": "XML",
      "schema": "N2VenditaCalore",
      "endpoint": "http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx"
    }
  },
  "staging": {
    "tables": ["ACSKPIProcess", "ACSKPIDetail", "ACSKPIConfiguration"],
    "stored_procedure": "ACSVenditaCaloreMilanoPollingStmt",
    "pattern": "Polling TOP 10 with retry logic and transaction status management",
    "acs_kpi_type": "VenditaCaloreMilano"
  },
  "transformations": [
    {
      "map": "ACSVenditaCaloreMilanoPollingToN2VenditaCalore.btm",
      "purpose": "Database polling result to NDUE SOAP format"
    },
    {
      "map": "ACSVenditaCaloreMilanoPollingToACSUpdateStatus.btm",
      "purpose": "Status update for processed records"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["VenditaCaloreMilanoProcess.odx"],
    "receive_ports": ["ACSVenditaCaloreMilanoPollingIn"],
    "send_ports": ["N2VenditaCaloreOut", "ACSUpdateStatusOut"],
    "schemas": ["ACSVenditaCaloreMilanoPolling.Type.xsd", "N2VenditaCalore.Type.xsd"],
    "maps": ["ACSVenditaCaloreMilanoPollingToN2VenditaCalore.btm", "ACSVenditaCaloreMilanoPollingToACSUpdateStatus.btm"],
    "bindings": ["N2VenditaCalore.BindingInfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/VenditaCaloreMilanoProcess.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/ACSVenditaCaloreMilanoPollingToN2VenditaCalore.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/ACSVenditaCaloreMilanoPollingToACSUpdateStatus.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreMilanoPolling.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/N2VenditaCalore.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2VenditaCalore.BindingInfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSVenditaCaloreMilanoPollingStmt.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameProcess for process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "Transaction status in ACSKPIProcess table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "TOP 10 records per polling cycle, <100 records/day typical",
    "sla": "Near real-time processing, <5min latency target",
    "resilience": "Automatic retry logic, transaction isolation READ COMMITTED",
    "deadletter": "Automatic filesystem backup + RouteFailedMessage=true",
    "schedule": "Continuous 24/7 polling with ScheduleRecurrenceInterval=1"
  },
  "risks": {
    "technical_debt": ["SOAP 1.1/1.2 legacy protocol", "BizTalk Server dependency", "Sequential polling TOP 10 limitation"],
    "scalability": "No parallelization, single-threaded processing",
    "maintainability": "Complex XML schemas and proprietary BizTalk mapping",
    "migration_notes": "Candidate for Azure Integration Services, REST APIs, Event-driven architecture",
    "operational_risks": ["Single point of failure on EAI database", "NDUE endpoint availability dependency"]
  }
},
{
  "process_name": "VenditaCaloreMilanoStaging",
  "purpose": "Staging di file dati vendita calore impianti Milano in tabelle database EAI per elaborazione successiva",
  "io": {
    "input": {
      "type": "File XML",
      "source": "File system",
      "format": "XML",
      "encoding": "UTF-16",
      "schema": "VenditaCaloreMilano",
      "pattern": "VenditaCalore-Milano-YYYY"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAI",
      "format": "XML",
      "schema": "ACSVenditaCaloreMilanoInsertResponse"
    }
  },
  "staging": {
    "tables": ["ACSKPIFile", "ACSKPIProcess", "ACSKPIDetail"],
    "stored_procedure": "ACSVenditaCaloreMilanoInsert",
    "pattern": "File metadata + Process control + Detail data hierarchy with UNPIVOT transformation",
    "acs_kpi_type": "VenditaCaloreMilano"
  },
  "transformations": [
    {
      "map": "VenditaCaloreMilanoToACSVenditaCaloreMilanoInsert.btm",
      "purpose": "File XML to SQL stored procedure parameters with ACSGenericRowType"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["VenditaCaloreMilanoStaging.odx"],
    "receive_ports": ["VenditaCaloreMilanoIn"],
    "send_ports": ["ACSVenditaCaloreMilanoInsertOut"],
    "schemas": ["VenditaCaloreMilano.xsd", "VenditaCaloreMilano.Type.xsd", "ACSVenditaCaloreMilanoInsertTypedProcedure.xsd"],
    "maps": ["VenditaCaloreMilanoToACSVenditaCaloreMilanoInsert.btm"],
    "bindings": ["ACSVenditaCaloreMilanoInsert.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Processes/VenditaCaloreMilanoStaging.odx",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Maps/VenditaCalore/VenditaCaloreMilanoToACSVenditaCaloreMilanoInsert.btm",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/VenditaCaloreMilano.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/VenditaCaloreMilano.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreMilanoInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Schemas/VenditaCalore/ACSVenditaCaloreMilanoInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSVenditaCaloreMilanoInsert.bindinginfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/ACSVenditaCaloreMilanoInsert.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/User Defined Types/ACSGenericRowType.sql",
    "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityNameStaging for staging process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata in ACSKPIFile table",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring"
  },
  "non_functional": {
    "volumes": "Single files with annual heat sales data for Milano plants, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation READ COMMITTED",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex XML schemas with generic v0-v17 fields", "Complex UNPIVOT stored procedure logic"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and complex SQL stored procedures",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAI database", "File system dependency for input", "Rigid filename validation"]
  }
},
{
  "process_name": "prcDatiLaboratorio",
  "purpose": "Integrazione dati laboratorio ambientale verso sistema PMC con filtro conformità normativa",
  "io": {
    "input": {
      "type": "File flat delimitato",
      "source": "File system",
      "format": "Flat file semicolon-separated",
      "encoding": "Windows-1252",
      "schema": "schDatiLaboratorio",
      "pattern": "unknown"
    },
    "output": {
      "type": "SQL Stored Procedure Response",
      "target": "Database EAISYN",
      "format": "XML",
      "schema": "DatiLaboratorioInsertResponse/DatiLaboratorioCheckResponse"
    }
  },
  "staging": {
    "tables": ["T_IPPCDati", "T_Microinquinanti", "T_IPPCTestataImpianti"],
    "stored_procedures": ["DatiLaboratorioCheck", "DatiLaboratorioInsert"],
    "pattern": "Check existing data + Insert with microinquinanti join + PMC filter",
    "database": "EAISYN"
  },
  "transformations": [
    {
      "map": "mapDatiLaboratorioToCheck.btm",
      "purpose": "File flat to DatiLaboratorioCheck stored procedure parameters"
    },
    {
      "map": "mapDatiLaboratorioToInsert.btm",
      "purpose": "File flat to DatiLaboratorioInsert stored procedure with DatiLaboratorioType"
    }
  ],
  "biztalk_artifacts": {
    "orchestrations": ["prcDatiLaboratorio.odx"],
    "receive_ports": ["rptDatiLaboratorio"],
    "send_ports": ["sptDatiLaboratorioCheck", "sptDatiLaboratorioInsert"],
    "schemas": ["schDatiLaboratorio.xsd", "schDatiLaboratorioCheckTypedProcedure.xsd", "schDatiLaboratorioInsertTypedProcedure.xsd"],
    "maps": ["mapDatiLaboratorioToCheck.btm", "mapDatiLaboratorioToInsert.btm"],
    "pipelines": ["rppDatiLaboratorio.btp"],
    "bindings": ["schDatiLaboratorioCheckTypedProcedure.bindinginfo.xml", "schDatiLaboratorioInsertTypedProcedure.bindinginfo.xml"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Processes/prcDatiLaboratorio.odx",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Maps/mapDatiLaboratorioToCheck.btm",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Maps/mapDatiLaboratorioToInsert.btm",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Schemas/DatiLaboratorio/schDatiLaboratorio.xsd",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Schemas/DatiLaboratorio/schDatiLaboratorioCheckTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Schemas/DatiLaboratorio/schDatiLaboratorioInsertTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Schemas/DatiLaboratorio/schDatiLaboratorioInsertTypedProcedure.Table.xsd",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Schemas/DatiLaboratorio/schDatiLaboratorioInsertTypedProcedure.Type.xsd",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Pipelines/rppDatiLaboratorio.btp",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Bindings/schDatiLaboratorioCheckTypedProcedure.bindinginfo.xml",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Bindings/schDatiLaboratorioInsertTypedProcedure.bindinginfo.xml",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/DatiLaboratorioCheck.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/DatiLaboratorioInsert.sql",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/User Defined Types/DatiLaboratorioType.sql",
    "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Services/ProcessServices.cs"
  ],
  "monitoring": {
    "bam": "ActivityName AMBIENTE for environmental process tracking",
    "trace_id": "activityInstanceId correlation",
    "message_tracking": "File metadata tracking via originalFileName",
    "notifications": "NotificationService for error alerting",
    "health_checks": "ProcessResult.Code status monitoring",
    "pmc_filter": "IsToProcess filter for VASCA TRAPPOLA exclusion"
  },
  "non_functional": {
    "volumes": "Single files with laboratory analysis data, size unknown",
    "sla": "Immediate processing post-file reception, <1min target",
    "resilience": "Transaction enabled, isolation READ COMMITTED",
    "deadletter": "Automatic backup via archiveFilePath + RouteFailedMessage=false",
    "schedule": "Event-driven file reception, no scheduled polling"
  },
  "risks": {
    "technical_debt": ["BizTalk Server dependency", "Complex flat file schema with Windows-1252 encoding", "Hardcoded PMC filter logic"],
    "scalability": "Sequential file processing, no parallelization",
    "maintainability": "BizTalk proprietary maps and complex SQL joins on T_Microinquinanti",
    "migration_notes": "Candidate for Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure on EAISYN database", "File system dependency for input", "Hardcoded VASCA TRAPPOLA filter"]
  }
},
{
  "process_name": "A2A.EAI.INT_BDE.Processes.prcBdeCb",
  "purpose": "Integrazione comandi bilanciamento BDE da Terna con validazione N2 PVMC",
  "io": {
    "input": {
      "type": "File flat delimitato",
      "path": "unknown",
      "format": "Flat file UTF-16 con delimitatori 0xA",
      "encoding": "UTF-16",
      "schema": "schTernaBde.xsd"
    },
    "output": {
      "database": "EAISYN.app.T_MessaggiBDE, T_MessaggiBDEPqnr",
      "webservice": "N2 CalcoloPVM endpoint SOAP",
      "notifications": "BAM tracking e notifiche errore"
    }
  },
  "staging": {
    "tables": ["EAISYN.app.T_MessaggiBDE", "EAISYN.app.T_MessaggiBDEPqnr", "EAISYN.app.T_TipoMessaggioBDE", "EAISYN.app.T_UnitaProduttiveIntegrazione"],
    "pattern": "Direct database insert con stored procedure BdeCb"
  },
  "transformations": {
    "maps": ["mapTernaBdeCbToEaiBdeCb", "mapEaiBdeCbToN2BdeCalcoloPVM"],
    "logic": "Flat file to SQL parameters + SQL to N2 SOAP request"
  },
  "biztalk_artifacts": {
    "orchestrations": ["prcBdeCb.odx"],
    "receive_ports": ["rptTernaBdeCb"],
    "send_ports": ["sptEaiBdeCb", "sptN2BdeCalcoloPvm", "sptNotification"],
    "schemas": ["schTernaBde.xsd", "schEaiBdeCbTypedProcedure.xsd", "schN2BdeCalcoloPVM.xsd"],
    "maps": ["mapTernaBdeCbToEaiBdeCb.btm", "mapEaiBdeCbToN2BdeCalcoloPVM.btm"],
    "pipelines": ["Microsoft.BizTalk.DefaultPipelines.XMLTransmit"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Processes/prcBdeCb.odx",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Maps/mapTernaBdeCbToEaiBdeCb.btm",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Maps/mapEaiBdeCbToN2BdeCalcoloPVM.btm",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schTernaBde.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schEaiBdeCbTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schN2BdeCalcoloPVM.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeCbTypedProcedure.bindinginfo.xml",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schN2WebServices.bindings.xml",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Services/ProcessServices.cs",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/BdeCb.sql"
  ],
  "monitoring": {
    "bam_activity": "BDE",
    "tracking_fields": ["Data Inizio", "BDE Codice", "BDE Data Inizio/Fine", "BDE Stato Comando", "BDE Tipo", "UP", "Inviato a N2", "Esito WS", "Durata DB/WS", "Retry"],
    "error_handling": "RouteFailedMessage=true, archiveFilePath backup"
  },
  "non_functional": {
    "volumes": "File singoli con comandi bilanciamento giornalieri, dimensione variabile",
    "sla": "Elaborazione immediata post-ricezione file, target <1min",
    "resilience": "Transazioni abilitate, isolamento Serializable, retry configurabile",
    "deadletter": "Backup automatico tramite archiveFilePath + RouteFailedMessage=true",
    "schedule": "Event-driven su ricezione file, nessun polling schedulato"
  },
  "risks": {
    "technical_debt": ["Dipendenza BizTalk Server", "Schema flat file UTF-16 complesso", "Endpoint SOAP legacy N2", "Stored procedure con logica UNPIVOT complessa"],
    "scalability": "Elaborazione sequenziale file, nessuna parallelizzazione",
    "maintainability": "Map BizTalk proprietarie e stored procedure complesse",
    "migration_notes": "Candidato per Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure su database EAI", "Dipendenza endpoint N2", "Validazione filename rigida", "Retry logic configurabile ma limitata"]
  }
},
{
  "process_name": "A2A.EAI.INT_BDE.Processes.prcBdeLb",
  "purpose": "Integrazione comandi limitazione bilanciamento BDE da Terna con validazione N2",
  "io": {
    "input": {
      "type": "File flat delimitato",
      "path": "unknown",
      "format": "Flat file UTF-16 con delimitatori 0xA",
      "encoding": "UTF-16",
      "schema": "schTernaBde.xsd"
    },
    "output": {
      "database": "EAISYN.app.T_MessaggiBDE con TipoBDE_ID=5",
      "webservice": "N2 ValidazioneBDE endpoint SOAP",
      "notifications": "BAM tracking e notifiche errore"
    }
  },
  "staging": {
    "tables": ["EAISYN.app.T_MessaggiBDE", "EAISYN.app.T_TipoMessaggioBDE", "EAISYN.app.T_UnitaProduttiveIntegrazione"],
    "pattern": "Direct database insert con stored procedure BdeLb"
  },
  "transformations": {
    "maps": ["mapTernaBdeLbToEaiBdeLb", "mapEaiBdeLbToN2BdeValidazione"],
    "logic": "Flat file to SQL parameters + SQL to N2 SOAP request"
  },
  "biztalk_artifacts": {
    "orchestrations": ["prcBdeLb.odx"],
    "receive_ports": ["rptTernaBdeLb"],
    "send_ports": ["sptEaiBdeLb", "sptN2BdeValidazione", "sptNotification"],
    "schemas": ["schTernaBde.xsd", "schEaiBdeLbTypedProcedure.xsd", "schN2BdeValidazione.xsd"],
    "maps": ["mapTernaBdeLbToEaiBdeLb.btm", "mapEaiBdeLbToN2BdeValidazione.btm"],
    "pipelines": ["Microsoft.BizTalk.DefaultPipelines.XMLTransmit"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Processes/prcBdeLb.odx",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Maps/mapTernaBdeLbToEaiBdeLb.btm",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Maps/mapEaiBdeLbToN2BdeValidazione.btm",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schTernaBde.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schEaiBdeLbTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schN2BdeValidazione.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeLbTypedProcedure.bindinginfo.xml",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schN2WebServices.bindings.xml",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Services/ProcessServices.cs",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/BdeLb.sql"
  ],
  "monitoring": {
    "bam_activity": "BDE",
    "tracking_fields": ["Data Inizio", "BDE Codice", "BDE Data Inizio/Fine", "BDE Tipo", "UP", "Inviato a N2", "Esito WS", "Durata DB/WS", "Retry"],
    "error_handling": "SOAP exception handling, archiveFilePath backup"
  },
  "non_functional": {
    "volumes": "File singoli con comandi limitazione giornalieri, dimensione ridotta",
    "sla": "Elaborazione immediata post-ricezione file, target <1min",
    "resilience": "Transazioni abilitate, isolamento READ COMMITTED, retry configurabile",
    "deadletter": "Backup automatico tramite archiveFilePath, gestione eccezioni SOAP",
    "schedule": "Event-driven su ricezione file, nessun polling schedulato"
  },
  "risks": {
    "technical_debt": ["Dipendenza BizTalk Server", "Schema flat file UTF-16 complesso", "Endpoint SOAP legacy N2", "Stored procedure con logica business TipoBDE_ID=5"],
    "scalability": "Elaborazione sequenziale file, nessuna parallelizzazione",
    "maintainability": "Map BizTalk proprietarie e stored procedure con logica specifica",
    "migration_notes": "Candidato per Azure Logic Apps, Azure Functions, Event Grid",
    "operational_risks": ["Single point of failure su database EAI", "Dipendenza endpoint N2", "Validazione filename rigida", "Gestione eccezioni SOAP limitata"]
  }
},
{
  "process_name": "A2A.EAI.INT_BDE.Processes.prcBdeNotification",
  "purpose": "Invio notifiche email per processi BDE con recupero dinamico destinatari",
  "io": {
    "input": {
      "type": "Messaggio XML interno",
      "path": "Chiamato da altri processi BDE",
      "format": "XML schEaiBdeNotification",
      "encoding": "UTF-8",
      "schema": "schEaiBdeNotification.xsd"
    },
    "output": {
      "email": "Email HTML tramite servizio mail esterno",
      "database": "Query T_UnitaProduttive per indirizzi email",
      "notifications": "BAM tracking e notifiche errore"
    }
  },
  "staging": {
    "tables": ["EAISYN.app.T_UnitaProduttiveIntegrazione", "N2UnitaProduttive"],
    "pattern": "Direct query per recupero indirizzi email BDE"
  },
  "transformations": {
    "maps": ["mapEaiBdeNotificationToEaiBdeGetEmailAddr", "mapEaiBdeNotificationToMailSend"],
    "logic": "XML notification to SQL parameters + XML to MailSend format"
  },
  "biztalk_artifacts": {
    "orchestrations": ["prcBdeNotification.odx"],
    "receive_ports": ["Nessuno - chiamato da altri processi"],
    "send_ports": ["sptEaiBdeGetEmailAddr", "sptMailBde", "sptNotification"],
    "schemas": ["schEaiBdeNotification.xsd", "schEaiBdeGetEmailAddrTypedProcedure.xsd", "MailSend.xsd"],
    "maps": ["mapEaiBdeNotificationToEaiBdeGetEmailAddr.btm", "mapEaiBdeNotificationToMailSend.btm"],
    "pipelines": ["sppMailBde.btp"]
  },
  "files_impattati": [
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Processes/prcBdeNotification.odx",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Maps/mapEaiBdeNotificationToEaiBdeGetEmailAddr.btm",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Maps/mapEaiBdeNotificationToMailSend.btm",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schEaiBdeNotification.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Schemas/schEaiBdeGetEmailAddrTypedProcedure.xsd",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeGetEmailAddrTypedProcedure.bindinginfo.xml",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Pipeline/sppMailBde.btp",
    "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Services/ProcessServices.cs",
    "Sviluppo/A2A.EAI.DB/EAI/dbo/Stored Procedures/BdeGetEmailAddr.sql"
  ],
  "monitoring": {
    "bam_activity": "MailSender con relazione a BDE activity",
    "tracking_fields": ["operation_status", "errorMessage", "value", "caller orchestration ID"],
    "error_handling": "Exception handling con notifica warning framework"
  },
  "non_functional": {
    "volumes": "Notifiche singole per ogni BDE processata, dimensione ridotta",
    "sla": "Invio immediato post-elaborazione BDE, target <30sec",
    "resilience": "Exception handling con notifica warning, nessun retry automatico",
    "deadletter": "Gestione eccezioni con notifica framework, nessun backup email",
    "schedule": "Event-driven su chiamata da altri processi BDE"
  },
  "risks": {
    "technical_debt": ["Dipendenza BizTalk Server", "Servizio mail esterno non specificato", "Hardcoded <NAME_EMAIL>", "Logica subject hardcoded in orchestrazione"],
    "scalability": "Invio sequenziale email, nessuna parallelizzazione",
    "maintainability": "Map BizTalk proprietarie e logica business in orchestrazione",
    "migration_notes": "Candidato per Azure Logic Apps, SendGrid, Office 365",
    "operational_risks": ["Single point of failure su servizio mail", "Dipendenza tabella T_UnitaProduttive", "Nessun retry su fallimento email", "Gestione eccezioni limitata"]
  }
}

}